import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  CardHeader,
  Stack,
  Skeleton,
} from '@mui/material';
import { EditOutlined } from '@mui/icons-material';
import {
  CheckCircle,
  CheckLg,
  ExclamationOctagon,
  ExclamationTriangle,
} from 'react-bootstrap-icons';
import { AssetIcon, SiteIcon } from '../Icons';
import {
  ConfigDeploymentResponse,
  ConfigInstanceStats,
  InstanceDetailsResponse,
} from '../../constants/types';

const CARD_STYLES = {
  card: {
    // width: '100%',
    bgcolor: 'common.white',
    border: '1px solid #d0d0d0',
    borderRadius: '6px',
    boxShadow: 'none',
    height: '-webkit-fill-available',
  },
  cardContent: {
    p: '10px 12px',
    '&:last-child': { pb: '10px' },
    height: '100%',
  },
  cardHeader: {
    width: '100%',
    padding: '0',
    '& .MuiTypography-root': {
      fontSize: '15px',
      fontWeight: 500,
      lineHeight: 1,
    },
    '& .MuiCardHeader-action': {
      all: 'unset',
    },
  },
  contentBox: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    mt: 1,
    gap: 1,
    width: '100%',
  },
};

const TYPOGRAPHY_STYLES = {
  label: {
    fontWeight: 500,
    fontSize: '11px',
    width: '100%',
  },
  value: {
    fontWeight: 400,
  },
  smallLabel: {
    fontWeight: 500,
    fontSize: '10px',
    width: '100%',
  },
  editIcon: {
    fontSize: 14,
    color: 'text.secondary',
    cursor: 'pointer',
  },
};

const LabelTypography = ({
  children,
  ...props
}: {
  children: React.ReactNode;
  [key: string]: any;
}) => (
  <Typography sx={TYPOGRAPHY_STYLES.label} {...props}>
    {children}
  </Typography>
);

const ValueTypography = ({
  children,
  ...props
}: {
  children: React.ReactNode;
  [key: string]: any;
}) => (
  <Typography variant='labelSmall' sx={TYPOGRAPHY_STYLES.value} {...props}>
    {children}
  </Typography>
);

const SmallLabelTypography = ({
  children,
  ...props
}: {
  children: React.ReactNode;
  [key: string]: any;
}) => (
  <Typography variant='labelSmall' sx={TYPOGRAPHY_STYLES.smallLabel} {...props}>
    {children}
  </Typography>
);

const HeaderCard = ({
  title,
  action,
  children,
}: {
  title: string;
  action: React.ReactNode;
  children: React.ReactNode;
}) => (
  <Card sx={CARD_STYLES.card}>
    <CardContent sx={CARD_STYLES.cardContent}>
      <CardHeader title={title} action={action} sx={CARD_STYLES.cardHeader} />
      <Box sx={CARD_STYLES.contentBox}>{children}</Box>
    </CardContent>
  </Card>
);

const InfoRow = ({ label, value }: { label: string; value: string }) => (
  <LabelTypography>
    {label}: <ValueTypography>{value}</ValueTypography>
  </LabelTypography>
);

const StatItem = ({
  label,
  value,
  icon,
  iconColor,
}: {
  label: string;
  value: string | number;
  icon: React.ReactElement;
  iconColor?: string;
}) => (
  <Stack
    direction='row'
    alignItems='center'
    spacing={0.5}
    sx={{ minWidth: 0, flex: 1, whiteSpace: 'nowrap' }}
  >
    <SmallLabelTypography>
      {label}
    </SmallLabelTypography>
    {React.cloneElement(icon as React.ReactElement, {
      ...(iconColor && { color: iconColor }),
        fontSize: icon.props.fontSize || 'small',
        sx: { fontSize: '1.5rem', ...icon.props.sx },
    })}
    <Typography variant='labelSmall' sx={{ fontSize: '11px', fontWeight: 400 }}>
      {value}
    </Typography>
  </Stack>
);

const InfoRowSkeleton = () => (
  <LabelTypography>
    <Skeleton width='60%' height='14px' />
  </LabelTypography>
);

const StatItemSkeleton = () => (
  <Stack
    direction='row'
    alignItems='center'
    spacing={0.5}
    sx={{ minWidth: 0, flex: 1, whiteSpace: 'nowrap' }}
  >
    <Skeleton width='50px' height='12px' />
    <Skeleton variant='circular' width='16px' height='16px' />
    <Skeleton width='20px' height='12px' />
  </Stack>
);

const HeaderCardSkeleton = ({ title }: { title: string }) => (
  <Card sx={CARD_STYLES.card}>
    <CardContent sx={CARD_STYLES.cardContent}>
      <CardHeader
        title={title}
        action={<Skeleton width='14px' height='14px' />}
        sx={CARD_STYLES.cardHeader}
      />
      <Box sx={CARD_STYLES.contentBox}>
        {title === 'Instance Details' ? (
          <>
            <InfoRowSkeleton />
            <InfoRowSkeleton />
          </>
        ) : (
          <Stack
            direction='row'
            spacing={3}
            width='100%'
            sx={{ justifyContent: 'space-between' }}
          >
            <StatItemSkeleton />
            <StatItemSkeleton />
            <StatItemSkeleton />
          </Stack>
        )}
      </Box>
    </CardContent>
  </Card>
);

const CompactHeaderCardsSkeleton = () => (
  <Box display='flex' gap={3} alignItems='center'>
    <HeaderCardSkeleton title='Instance Details' />
    <HeaderCardSkeleton title='Assignments' />
    <HeaderCardSkeleton title='Deployment' />
  </Box>
);

interface CompactHeaderCardsProps {
  instanceDetails?: InstanceDetailsResponse;
  topLevelAssignmentStats?: ConfigInstanceStats;
  deployments?: ConfigDeploymentResponse;
  onAssignmentClick: () => void;
  onInstanceDetailsClick: () => void;
  isLoading?: boolean;
}

const CompactHeaderCards = ({
  instanceDetails,
  topLevelAssignmentStats,
  deployments,
  onAssignmentClick,
  onInstanceDetailsClick,
  isLoading = false,
}: CompactHeaderCardsProps) => {
  const formatVersionNumber = (num: number) => {
    if (typeof num !== 'number') return '0000';
    return num.toString().padStart(4, '0');
  };

  if (
    isLoading ||
    !instanceDetails ||
    !topLevelAssignmentStats ||
    !deployments
  ) {
    return <CompactHeaderCardsSkeleton />;
  }

  return (
    <Box display='flex' gap={3} alignItems='center'>
      {/* Instance Details Card */}
      <HeaderCard
        title='Instance Details'
        action={
          <EditOutlined
            sx={TYPOGRAPHY_STYLES.editIcon}
            onClick={onInstanceDetailsClick}
          />
        }
      >
        <InfoRow label='App' value={instanceDetails?.appName} />
        <InfoRow
          label='Global Revision'
          value={formatVersionNumber(instanceDetails?.configurationVersion)}
        />
      </HeaderCard>

      {/* Assignments Card */}
      <HeaderCard
        title='Assignments'
        action={
          <EditOutlined
            sx={TYPOGRAPHY_STYLES.editIcon}
            onClick={onAssignmentClick}
          />
        }
      >
        <Stack
          direction='row'
          spacing={2}
          width='100%'
          sx={{ justifyContent: 'space-between' }}
        >
          <StatItem
            label='Applicable'
            value={topLevelAssignmentStats?.applicable}
            icon={<CheckCircle size={20} />}
            iconColor='#05A952'
          />
          <StatItem
            label='Overridden'
            value={topLevelAssignmentStats?.overridden}
            icon={<ExclamationTriangle />}
            iconColor='#F56733'
          />
          <StatItem
            label='Not Matching'
            value='1'
            icon={<ExclamationOctagon />}
            iconColor='#FF3B30'
          />
        </Stack>
      </HeaderCard>

      {/* Deployment Card */}
      <HeaderCard
        title='Deployment'
        action={
          <Typography
            color='primary'
            sx={{
              fontSize: '11px !important',
              cursor: 'pointer',
            }}
          >
            Details
          </Typography>
        }
      >
        <Stack
          direction='row'
          spacing={3}
          width='100%'
          sx={{ justifyContent: 'space-between' }}
        >
          <StatItem
            label='Sites'
            value={deployments?.siteCount}
            icon={<SiteIcon />}
          />
          <StatItem
            label='Assets'
            value={deployments?.assetsCount}
            icon={<AssetIcon />}
          />
          <StatItem
            label='Completed'
            value='1'
            icon={<CheckLg size={20} />}
            iconColor='#35A072'
          />
        </Stack>
      </HeaderCard>
    </Box>
  );
};

export default CompactHeaderCards;
